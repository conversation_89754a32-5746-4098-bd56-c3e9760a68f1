@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* تعريف نظام التصميم - برنامج المحاسبة اليومي */

@layer base {
  :root {
    /* الألوان الأساسية - نظام أخضر وأزرق مهني */
    --background: 210 25% 98%;
    --foreground: 210 25% 8%;

    --card: 0 0% 100%;
    --card-foreground: 210 25% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 25% 8%;

    /* أخضر مهني للمالية */
    --primary: 158 64% 25%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 158 45% 45%;
    --primary-glow: 158 45% 55%;

    /* أزرق ثانوي */
    --secondary: 210 40% 92%;
    --secondary-foreground: 210 25% 12%;
    --secondary-accent: 210 100% 45%;

    --muted: 210 25% 95%;
    --muted-foreground: 210 15% 55%;

    --accent: 210 25% 95%;
    --accent-foreground: 210 25% 12%;

    /* أحمر للديون والتحذيرات */
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 98%;

    /* أخضر للأرباح */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    /* برتقالي للتنبيهات */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    --border: 210 25% 88%;
    --input: 210 25% 92%;
    --ring: 158 64% 25%;

    /* التدرجات */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary-accent)), hsl(210 80% 55%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 60% 45%));
    --gradient-background: linear-gradient(180deg, hsl(var(--background)), hsl(210 20% 96%));

    /* الظلال */
    --shadow-primary: 0 10px 25px hsl(var(--primary) / 0.15);
    --shadow-card: 0 4px 12px hsl(210 15% 15% / 0.08);
    --shadow-elevated: 0 8px 32px hsl(210 15% 15% / 0.12);

    /* الانتقالات */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    background: var(--gradient-background);
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
  }

  /* عناصر مخصصة للتطبيق العربي */
  .font-arabic {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-success {
    background: var(--gradient-success);
  }

  .shadow-primary {
    box-shadow: var(--shadow-primary);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-elevated {
    box-shadow: var(--shadow-elevated);
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* تحسينات للجداول العربية */
  .table-rtl {
    direction: rtl;
  }

  .table-rtl th {
    text-align: right;
  }

  .table-rtl td {
    text-align: right;
  }

  /* تحسينات للأرقام */
  .numbers-ltr {
    direction: ltr;
    unicode-bidi: embed;
  }

  /* تحسينات للهواتف المحمولة */
  @media (max-width: 768px) {
    body {
      font-size: 14px;
    }

    /* تحسين الأزرار للمس */
    button, .btn {
      min-height: 44px;
      min-width: 44px;
      padding: 12px 16px;
    }

    /* تحسين الحقول للمس */
    input, select, textarea {
      min-height: 44px;
      font-size: 16px; /* منع التكبير في iOS */
    }

    /* تحسين الجداول للشاشات الصغيرة */
    .table-responsive {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    /* تحسين البطاقات للهواتف */
    .card-mobile {
      margin: 8px;
      border-radius: 12px;
    }

    /* تحسين التخطيط للشاشات الصغيرة */
    .mobile-stack {
      flex-direction: column !important;
      gap: 12px !important;
    }

    /* تحسين النصوص للشاشات الصغيرة */
    .text-mobile-sm {
      font-size: 12px;
    }

    .text-mobile-base {
      font-size: 14px;
    }

    .text-mobile-lg {
      font-size: 16px;
    }

    /* تحسين المسافات للهواتف */
    .p-mobile {
      padding: 12px;
    }

    .m-mobile {
      margin: 8px;
    }
  }

  /* تحسينات للشاشات الصغيرة جداً */
  @media (max-width: 480px) {
    .container {
      padding: 8px;
    }

    /* تصغير الخطوط للشاشات الصغيرة جداً */
    h1 {
      font-size: 1.5rem;
    }

    h2 {
      font-size: 1.25rem;
    }

    h3 {
      font-size: 1.125rem;
    }

    /* تحسين الشبكة للشاشات الصغيرة */
    .grid-mobile-1 {
      grid-template-columns: 1fr !important;
    }
  }

  /* تحسينات اللمس */
  .touch-friendly {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
  }

  /* تحسين التمرير */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}