import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { Transaction, Customer, Product, AppState } from '@/types';
import { saveToStorage, loadFromStorage, STORAGE_KEYS } from '@/lib/storage';

interface AppStore extends AppState {
  // Actions للمعاملات
  addTransaction: (transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateTransaction: (id: string, updates: Partial<Transaction>) => void;
  deleteTransaction: (id: string) => void;
  getTransactionsByCustomer: (customerName: string) => Transaction[];
  getTransactionsByDateRange: (startDate: string, endDate: string) => Transaction[];

  // Actions للعملاء
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCustomer: (id: string, updates: Partial<Customer>) => void;
  deleteCustomer: (id: string) => void;
  getCustomerByName: (name: string) => Customer | undefined;
  updateCustomerStats: (customerName: string) => void;

  // Actions للمنتجات
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, updates: Partial<Product>) => void;
  deleteProduct: (id: string) => void;
  getProductByName: (name: string) => Product | undefined;
  updateProductStats: (productName: string) => void;

  // Actions عامة
  loadData: () => void;
  saveData: () => void;
  clearAllData: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAppStore = create<AppStore>((set, get) => ({
  // الحالة الأولية
  transactions: [],
  customers: [],
  products: [],
  isLoading: false,
  lastUpdated: new Date().toISOString(),

  // Actions للمعاملات
  addTransaction: (transactionData) => {
    const now = new Date().toISOString();
    const transaction: Transaction = {
      ...transactionData,
      id: uuidv4(),
      createdAt: now,
      updatedAt: now,
    };

    set((state) => {
      const newTransactions = [...state.transactions, transaction];
      saveToStorage(STORAGE_KEYS.TRANSACTIONS, newTransactions);
      return {
        transactions: newTransactions,
        lastUpdated: now,
      };
    });

    // تحديث إحصائيات العميل والمنتج
    get().updateCustomerStats(transaction.customerName);
    get().updateProductStats(transaction.product);
  },

  updateTransaction: (id, updates) => {
    const now = new Date().toISOString();
    set((state) => {
      const newTransactions = state.transactions.map((t) =>
        t.id === id ? { ...t, ...updates, updatedAt: now } : t
      );
      saveToStorage(STORAGE_KEYS.TRANSACTIONS, newTransactions);
      return {
        transactions: newTransactions,
        lastUpdated: now,
      };
    });
  },

  deleteTransaction: (id) => {
    const now = new Date().toISOString();
    set((state) => {
      const newTransactions = state.transactions.filter((t) => t.id !== id);
      saveToStorage(STORAGE_KEYS.TRANSACTIONS, newTransactions);
      return {
        transactions: newTransactions,
        lastUpdated: now,
      };
    });
  },

  getTransactionsByCustomer: (customerName) => {
    return get().transactions.filter((t) => t.customerName === customerName);
  },

  getTransactionsByDateRange: (startDate, endDate) => {
    return get().transactions.filter((t) => t.date >= startDate && t.date <= endDate);
  },

  // Actions للعملاء
  addCustomer: (customerData) => {
    const now = new Date().toISOString();
    const customer: Customer = {
      ...customerData,
      id: uuidv4(),
      createdAt: now,
      updatedAt: now,
    };

    set((state) => {
      const newCustomers = [...state.customers, customer];
      saveToStorage(STORAGE_KEYS.CUSTOMERS, newCustomers);
      return {
        customers: newCustomers,
        lastUpdated: now,
      };
    });
  },

  updateCustomer: (id, updates) => {
    const now = new Date().toISOString();
    set((state) => {
      const newCustomers = state.customers.map((c) =>
        c.id === id ? { ...c, ...updates, updatedAt: now } : c
      );
      saveToStorage(STORAGE_KEYS.CUSTOMERS, newCustomers);
      return {
        customers: newCustomers,
        lastUpdated: now,
      };
    });
  },

  deleteCustomer: (id) => {
    const now = new Date().toISOString();
    set((state) => {
      const newCustomers = state.customers.filter((c) => c.id !== id);
      saveToStorage(STORAGE_KEYS.CUSTOMERS, newCustomers);
      return {
        customers: newCustomers,
        lastUpdated: now,
      };
    });
  },

  getCustomerByName: (name) => {
    return get().customers.find((c) => c.name === name);
  },

  updateCustomerStats: (customerName) => {
    const transactions = get().getTransactionsByCustomer(customerName);
    const customer = get().getCustomerByName(customerName);

    if (!customer && transactions.length > 0) {
      // إنشاء عميل جديد إذا لم يكن موجوداً
      const totalPurchases = transactions.reduce((sum, t) => sum + t.totalAmount, 0);
      const totalPaid = transactions.reduce((sum, t) => sum + t.paidAmount, 0);
      const lastTransaction = transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

      get().addCustomer({
        name: customerName,
        totalTransactions: transactions.length,
        totalPurchases,
        totalPaid,
        remainingDebt: totalPurchases - totalPaid,
        lastTransaction: lastTransaction.date,
      });
    } else if (customer) {
      // تحديث إحصائيات العميل الموجود
      const totalPurchases = transactions.reduce((sum, t) => sum + t.totalAmount, 0);
      const totalPaid = transactions.reduce((sum, t) => sum + t.paidAmount, 0);
      const lastTransaction = transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

      get().updateCustomer(customer.id, {
        totalTransactions: transactions.length,
        totalPurchases,
        totalPaid,
        remainingDebt: totalPurchases - totalPaid,
        lastTransaction: lastTransaction?.date || customer.lastTransaction,
      });
    }
  },

  // Actions للمنتجات
  addProduct: (productData) => {
    const now = new Date().toISOString();
    const product: Product = {
      ...productData,
      id: uuidv4(),
      createdAt: now,
      updatedAt: now,
    };

    set((state) => {
      const newProducts = [...state.products, product];
      saveToStorage(STORAGE_KEYS.PRODUCTS, newProducts);
      return {
        products: newProducts,
        lastUpdated: now,
      };
    });
  },

  updateProduct: (id, updates) => {
    const now = new Date().toISOString();
    set((state) => {
      const newProducts = state.products.map((p) =>
        p.id === id ? { ...p, ...updates, updatedAt: now } : p
      );
      saveToStorage(STORAGE_KEYS.PRODUCTS, newProducts);
      return {
        products: newProducts,
        lastUpdated: now,
      };
    });
  },

  deleteProduct: (id) => {
    const now = new Date().toISOString();
    set((state) => {
      const newProducts = state.products.filter((p) => p.id !== id);
      saveToStorage(STORAGE_KEYS.PRODUCTS, newProducts);
      return {
        products: newProducts,
        lastUpdated: now,
      };
    });
  },

  getProductByName: (name) => {
    return get().products.find((p) => p.name === name);
  },

  updateProductStats: (productName) => {
    const transactions = get().transactions.filter((t) => t.product === productName);
    const product = get().getProductByName(productName);

    if (product && transactions.length > 0) {
      const totalSold = transactions.reduce((sum, t) => sum + t.quantity, 0);
      const revenue = transactions.reduce((sum, t) => sum + t.totalAmount, 0);
      const lastSale = transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

      get().updateProduct(product.id, {
        totalSold,
        revenue,
        lastSale: lastSale.date,
      });
    }
  },

  // Actions عامة
  loadData: () => {
    set({ isLoading: true });
    
    const transactions = loadFromStorage<Transaction[]>(STORAGE_KEYS.TRANSACTIONS, []);
    const customers = loadFromStorage<Customer[]>(STORAGE_KEYS.CUSTOMERS, []);
    const products = loadFromStorage<Product[]>(STORAGE_KEYS.PRODUCTS, []);

    set({
      transactions,
      customers,
      products,
      isLoading: false,
      lastUpdated: new Date().toISOString(),
    });
  },

  saveData: () => {
    const state = get();
    saveToStorage(STORAGE_KEYS.TRANSACTIONS, state.transactions);
    saveToStorage(STORAGE_KEYS.CUSTOMERS, state.customers);
    saveToStorage(STORAGE_KEYS.PRODUCTS, state.products);
    
    set({ lastUpdated: new Date().toISOString() });
  },

  clearAllData: () => {
    set({
      transactions: [],
      customers: [],
      products: [],
      lastUpdated: new Date().toISOString(),
    });
    
    saveToStorage(STORAGE_KEYS.TRANSACTIONS, []);
    saveToStorage(STORAGE_KEYS.CUSTOMERS, []);
    saveToStorage(STORAGE_KEYS.PRODUCTS, []);
  },

  setLoading: (loading) => {
    set({ isLoading: loading });
  },
}));
