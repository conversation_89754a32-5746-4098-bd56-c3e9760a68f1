import { useMemo } from 'react';
import { useAppStore } from '@/stores/useAppStore';
import { DashboardStats, ReportData } from '@/types';

// Hook لحساب إحصائيات لوحة التحكم
export const useDashboardStats = (): DashboardStats => {
  const { transactions, customers, products } = useAppStore();

  return useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM

    // مبيعات اليوم
    const todayTransactions = transactions.filter(t => t.date === today);
    const todaySales = todayTransactions.reduce((sum, t) => sum + t.totalAmount, 0);

    // إجمالي الديون
    const totalDebts = customers.reduce((sum, c) => sum + c.remainingDebt, 0);

    // مبيعات الشهر الحالي
    const thisMonthTransactions = transactions.filter(t => t.date.startsWith(currentMonth));
    const thisMonthSales = thisMonthTransactions.reduce((sum, t) => sum + t.totalAmount, 0);

    return {
      todaySales,
      totalDebts,
      thisMonthSales,
      customersCount: customers.length,
      productsCount: products.length,
      transactionsCount: transactions.length,
    };
  }, [transactions, customers, products]);
};

// Hook لحساب تقارير يومية أو شهرية
export const useReportData = (type: 'daily' | 'monthly', date?: string): ReportData => {
  const { transactions, customers, products } = useAppStore();

  return useMemo(() => {
    let filteredTransactions = transactions;

    if (type === 'daily') {
      const targetDate = date || new Date().toISOString().split('T')[0];
      filteredTransactions = transactions.filter(t => t.date === targetDate);
    } else if (type === 'monthly') {
      const targetMonth = date || new Date().toISOString().slice(0, 7);
      filteredTransactions = transactions.filter(t => t.date.startsWith(targetMonth));
    }

    // الإحصائيات الأساسية
    const totalSales = filteredTransactions.reduce((sum, t) => sum + t.totalAmount, 0);
    const totalPaid = filteredTransactions.reduce((sum, t) => sum + t.paidAmount, 0);
    const totalRemaining = totalSales - totalPaid;

    // أفضل المنتجات
    const productStats = new Map<string, { quantity: number; revenue: number }>();
    filteredTransactions.forEach(t => {
      const existing = productStats.get(t.product) || { quantity: 0, revenue: 0 };
      productStats.set(t.product, {
        quantity: existing.quantity + t.quantity,
        revenue: existing.revenue + t.totalAmount,
      });
    });

    const topProducts = Array.from(productStats.entries())
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // أفضل العملاء
    const customerStats = new Map<string, { transactions: number; total: number }>();
    filteredTransactions.forEach(t => {
      const existing = customerStats.get(t.customerName) || { transactions: 0, total: 0 };
      customerStats.set(t.customerName, {
        transactions: existing.transactions + 1,
        total: existing.total + t.totalAmount,
      });
    });

    const topCustomers = Array.from(customerStats.entries())
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.total - a.total)
      .slice(0, 5);

    return {
      totalSales,
      totalTransactions: filteredTransactions.length,
      totalPaid,
      totalRemaining,
      topProducts,
      topCustomers,
    };
  }, [transactions, type, date]);
};

// Hook للبحث والتصفية
export const useFilteredTransactions = (
  searchTerm: string = '',
  customerFilter: string = '',
  dateFilter: string = ''
) => {
  const { transactions } = useAppStore();

  return useMemo(() => {
    return transactions.filter(transaction => {
      const matchesSearch = 
        transaction.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.product.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCustomer = !customerFilter || customerFilter === 'all' || 
        transaction.customerName === customerFilter;
      
      const matchesDate = !dateFilter || transaction.date === dateFilter;
      
      return matchesSearch && matchesCustomer && matchesDate;
    });
  }, [transactions, searchTerm, customerFilter, dateFilter]);
};

// Hook للبحث في العملاء
export const useFilteredCustomers = (searchTerm: string = '') => {
  const { customers } = useAppStore();

  return useMemo(() => {
    return customers.filter(customer =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [customers, searchTerm]);
};

// Hook للبحث في المنتجات
export const useFilteredProducts = (searchTerm: string = '') => {
  const { products } = useAppStore();

  return useMemo(() => {
    return products.filter(product =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [products, searchTerm]);
};

// Hook لحساب إحصائيات المعاملات
export const useTransactionStats = (transactions: any[]) => {
  return useMemo(() => {
    const totalAmount = transactions.reduce((sum, t) => sum + t.totalAmount, 0);
    const totalPaid = transactions.reduce((sum, t) => sum + t.paidAmount, 0);
    const totalRemaining = transactions.reduce((sum, t) => sum + t.remainingAmount, 0);

    return {
      totalTransactions: transactions.length,
      totalAmount,
      totalPaid,
      totalRemaining,
    };
  }, [transactions]);
};

// Hook للحصول على قائمة العملاء الفريدة
export const useUniqueCustomers = () => {
  const { transactions } = useAppStore();

  return useMemo(() => {
    const uniqueCustomers = [...new Set(transactions.map(t => t.customerName))];
    return uniqueCustomers.sort();
  }, [transactions]);
};

// Hook للحصول على قائمة المنتجات الفريدة
export const useUniqueProducts = () => {
  const { transactions, products } = useAppStore();

  return useMemo(() => {
    // دمج المنتجات من المعاملات والمنتجات المحفوظة
    const transactionProducts = transactions.map(t => t.product);
    const savedProducts = products.map(p => p.name);
    const allProducts = [...new Set([...transactionProducts, ...savedProducts])];
    return allProducts.sort();
  }, [transactions, products]);
};
