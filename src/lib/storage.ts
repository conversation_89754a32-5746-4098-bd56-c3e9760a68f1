// مساعدات التخزين المحلي

const STORAGE_KEYS = {
  TRANSACTIONS: 'trend-brand-transactions',
  CUSTOMERS: 'trend-brand-customers',
  PRODUCTS: 'trend-brand-products',
  APP_STATE: 'trend-brand-app-state',
} as const;

// حفظ البيانات في localStorage
export const saveToStorage = <T>(key: string, data: T): void => {
  try {
    const serializedData = JSON.stringify(data);
    localStorage.setItem(key, serializedData);
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

// قراءة البيانات من localStorage
export const loadFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }
    return JSON.parse(item) as T;
  } catch (error) {
    console.error('Error loading from localStorage:', error);
    return defaultValue;
  }
};

// حذف البيانات من localStorage
export const removeFromStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing from localStorage:', error);
  }
};

// مسح جميع بيانات التطبيق
export const clearAllAppData = (): void => {
  Object.values(STORAGE_KEYS).forEach(key => {
    removeFromStorage(key);
  });
};

// التحقق من وجود البيانات
export const hasStoredData = (key: string): boolean => {
  try {
    return localStorage.getItem(key) !== null;
  } catch (error) {
    console.error('Error checking localStorage:', error);
    return false;
  }
};

// تصدير مفاتيح التخزين
export { STORAGE_KEYS };

// حساب حجم البيانات المخزنة
export const getStorageSize = (): number => {
  try {
    let totalSize = 0;
    Object.values(STORAGE_KEYS).forEach(key => {
      const item = localStorage.getItem(key);
      if (item) {
        totalSize += item.length;
      }
    });
    return totalSize;
  } catch (error) {
    console.error('Error calculating storage size:', error);
    return 0;
  }
};

// تصدير البيانات كـ JSON
export const exportData = () => {
  try {
    const data = {
      transactions: loadFromStorage(STORAGE_KEYS.TRANSACTIONS, []),
      customers: loadFromStorage(STORAGE_KEYS.CUSTOMERS, []),
      products: loadFromStorage(STORAGE_KEYS.PRODUCTS, []),
      exportDate: new Date().toISOString(),
    };
    return JSON.stringify(data, null, 2);
  } catch (error) {
    console.error('Error exporting data:', error);
    return null;
  }
};

// استيراد البيانات من JSON
export const importData = (jsonData: string): boolean => {
  try {
    const data = JSON.parse(jsonData);
    
    if (data.transactions) {
      saveToStorage(STORAGE_KEYS.TRANSACTIONS, data.transactions);
    }
    if (data.customers) {
      saveToStorage(STORAGE_KEYS.CUSTOMERS, data.customers);
    }
    if (data.products) {
      saveToStorage(STORAGE_KEYS.PRODUCTS, data.products);
    }
    
    return true;
  } catch (error) {
    console.error('Error importing data:', error);
    return false;
  }
};
