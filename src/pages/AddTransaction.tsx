import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Plus, ArrowRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Link, useNavigate } from "react-router-dom";
import { useAppStore } from "@/stores/useAppStore";
import { useUniqueCustomers, useUniqueProducts } from "@/hooks/useStats";

const AddTransaction = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const addTransaction = useAppStore((state) => state.addTransaction);
  const uniqueCustomers = useUniqueCustomers();
  const uniqueProducts = useUniqueProducts();

  const [formData, setFormData] = useState({
    customerName: "",
    product: "",
    quantity: "",
    pricePerUnit: "",
    totalAmount: "",
    paidAmount: "",
    remainingAmount: "",
    date: new Date().toISOString().split('T')[0],
    notes: "",
    paymentMethod: "cash",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // حساب المبلغ الكلي تلقائياً
      if (field === "quantity" || field === "pricePerUnit") {
        const quantity = parseFloat(field === "quantity" ? value : updated.quantity) || 0;
        const price = parseFloat(field === "pricePerUnit" ? value : updated.pricePerUnit) || 0;
        updated.totalAmount = (quantity * price).toString();
      }
      
      // حساب المبلغ المتبقي تلقائياً
      if (field === "totalAmount" || field === "paidAmount") {
        const total = parseFloat(field === "totalAmount" ? value : updated.totalAmount) || 0;
        const paid = parseFloat(field === "paidAmount" ? value : updated.paidAmount) || 0;
        updated.remainingAmount = (total - paid).toString();
      }
      
      return updated;
    });
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = "اسم العميل مطلوب";
    }

    if (!formData.product.trim()) {
      newErrors.product = "اسم المنتج مطلوب";
    }

    if (!formData.quantity || parseFloat(formData.quantity) <= 0) {
      newErrors.quantity = "الكمية يجب أن تكون أكبر من صفر";
    }

    if (!formData.pricePerUnit || parseFloat(formData.pricePerUnit) <= 0) {
      newErrors.pricePerUnit = "سعر الوحدة يجب أن يكون أكبر من صفر";
    }

    if (!formData.totalAmount || parseFloat(formData.totalAmount) <= 0) {
      newErrors.totalAmount = "المبلغ الإجمالي يجب أن يكون أكبر من صفر";
    }

    if (formData.paidAmount && parseFloat(formData.paidAmount) < 0) {
      newErrors.paidAmount = "المبلغ المدفوع لا يمكن أن يكون سالباً";
    }

    if (parseFloat(formData.paidAmount || "0") > parseFloat(formData.totalAmount || "0")) {
      newErrors.paidAmount = "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الإجمالي";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من صحة البيانات
    if (!validateForm()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى تصحيح الأخطاء المعروضة",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // إنشاء كائن المعاملة
      const transactionData = {
        customerName: formData.customerName,
        product: formData.product,
        quantity: parseFloat(formData.quantity),
        pricePerUnit: parseFloat(formData.pricePerUnit),
        totalAmount: parseFloat(formData.totalAmount),
        paidAmount: parseFloat(formData.paidAmount) || 0,
        remainingAmount: parseFloat(formData.remainingAmount) || 0,
        date: formData.date,
        time: new Date().toLocaleTimeString('ar-SA', {
          hour: '2-digit',
          minute: '2-digit'
        }),
      };

      // حفظ المعاملة
      addTransaction(transactionData);

      toast({
        title: "تم إضافة العملية بنجاح",
        description: `تم تسجيل عملية بيع ${formData.product} للعميل ${formData.customerName}`,
      });

      // إعادة تعيين النموذج
      setFormData({
        customerName: "",
        product: "",
        quantity: "",
        pricePerUnit: "",
        totalAmount: "",
        paidAmount: "",
        remainingAmount: "",
        date: new Date().toISOString().split('T')[0],
        notes: "",
        paymentMethod: "cash",
      });
      setErrors({});

      // التوجه إلى صفحة المعاملات بعد 2 ثانية
      setTimeout(() => {
        navigate('/transactions');
      }, 2000);

    } catch (error) {
      console.error('Error saving transaction:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ المعاملة. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center gap-4">
        <Link to="/">
          <Button variant="ghost" size="sm">
            <ArrowRight className="w-4 h-4" />
            العودة للرئيسية
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-foreground">إضافة عملية بيع جديدة</h1>
          <p className="text-muted-foreground">تسجيل عملية بيع جديدة مع تفاصيل الدفع</p>
        </div>
      </div>

      <Card className="p-8 shadow-card">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* معلومات العميل */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-primary"></div>
                معلومات العميل
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="customer">اسم التاجر / العميل *</Label>
                <Select 
                  value={formData.customerName} 
                  onValueChange={(value) => handleInputChange("customerName", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر عميل موجود أو اكتب اسم جديد" />
                  </SelectTrigger>
                  <SelectContent>
                    {uniqueCustomers.map((customer) => (
                      <SelectItem key={customer} value={customer}>
                        {customer}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  placeholder="أو اكتب اسم عميل جديد"
                  value={formData.customerName}
                  onChange={(e) => handleInputChange("customerName", e.target.value)}
                  className={errors.customerName ? "border-destructive" : ""}
                />
                {errors.customerName && (
                  <p className="text-sm text-destructive">{errors.customerName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">تاريخ العملية</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="paymentMethod">طريقة الدفع</Label>
                <Select
                  value={formData.paymentMethod}
                  onValueChange={(value) => handleInputChange("paymentMethod", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر طريقة الدفع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">نقداً</SelectItem>
                    <SelectItem value="credit">آجل</SelectItem>
                    <SelectItem value="partial">دفع جزئي</SelectItem>
                    <SelectItem value="bank">تحويل بنكي</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* معلومات المنتج */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-success"></div>
                معلومات المنتج
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="product">نوع المنتج *</Label>
                <Select 
                  value={formData.product} 
                  onValueChange={(value) => handleInputChange("product", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المنتج" />
                  </SelectTrigger>
                  <SelectContent>
                    {uniqueProducts.map((product) => (
                      <SelectItem key={product} value={product}>
                        {product}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  placeholder="أو اكتب اسم منتج جديد"
                  value={formData.product}
                  onChange={(e) => handleInputChange("product", e.target.value)}
                  className={errors.product ? "border-destructive" : ""}
                />
                {errors.product && (
                  <p className="text-sm text-destructive">{errors.product}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">الكمية *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    placeholder="50"
                    value={formData.quantity}
                    onChange={(e) => handleInputChange("quantity", e.target.value)}
                    className={errors.quantity ? "border-destructive" : ""}
                  />
                  {errors.quantity ? (
                    <p className="text-xs text-destructive">{errors.quantity}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground">بالكيلوغرام</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pricePerUnit">السعر للكيلو *</Label>
                  <Input
                    id="pricePerUnit"
                    type="number"
                    step="0.01"
                    placeholder="10.50"
                    value={formData.pricePerUnit}
                    onChange={(e) => handleInputChange("pricePerUnit", e.target.value)}
                    className={errors.pricePerUnit ? "border-destructive" : ""}
                  />
                  {errors.pricePerUnit ? (
                    <p className="text-xs text-destructive">{errors.pricePerUnit}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground">جنيه مصري</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* معلومات مالية */}
          <div className="border-t border-border pt-6">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2 mb-4">
              <div className="w-2 h-2 rounded-full bg-warning"></div>
              المعلومات المالية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="totalAmount">المبلغ الكلي *</Label>
                <Input
                  id="totalAmount"
                  type="number"
                  step="0.01"
                  placeholder="525.00"
                  value={formData.totalAmount}
                  onChange={(e) => handleInputChange("totalAmount", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">سيتم حسابه تلقائياً</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="paidAmount">المبلغ المدفوع</Label>
                <Input
                  id="paidAmount"
                  type="number"
                  step="0.01"
                  placeholder="400.00"
                  value={formData.paidAmount}
                  onChange={(e) => handleInputChange("paidAmount", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">المبلغ المدفوع فعلياً</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="remainingAmount">المبلغ المتبقي</Label>
                <Input
                  id="remainingAmount"
                  type="number"
                  step="0.01"
                  value={formData.remainingAmount}
                  readOnly
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">سيتم حسابه تلقائياً</p>
              </div>
            </div>

            {/* حقل الملاحظات */}
            <div className="space-y-2">
              <Label htmlFor="notes">ملاحظات إضافية</Label>
              <textarea
                id="notes"
                placeholder="أي ملاحظات أو تفاصيل إضافية..."
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                className="w-full min-h-[80px] px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-md resize-none"
                rows={3}
              />
            </div>

            {/* ملخص المعاملة */}
            {formData.totalAmount && (
              <div className="mt-6 p-4 bg-accent rounded-lg">
                <h4 className="font-medium text-accent-foreground mb-2">ملخص المعاملة:</h4>
                <div className="space-y-1 text-sm">
                  <p>العميل: <span className="font-medium">{formData.customerName}</span></p>
                  <p>المنتج: <span className="font-medium">{formData.product}</span></p>
                  <p>الكمية: <span className="font-medium numbers-ltr">{formData.quantity} كيلو</span></p>
                  <p>طريقة الدفع: <span className="font-medium">
                    {formData.paymentMethod === 'cash' && 'نقداً'}
                    {formData.paymentMethod === 'credit' && 'آجل'}
                    {formData.paymentMethod === 'partial' && 'دفع جزئي'}
                    {formData.paymentMethod === 'bank' && 'تحويل بنكي'}
                  </span></p>
                  <p>المبلغ الكلي: <span className="font-medium numbers-ltr text-primary">{parseFloat(formData.totalAmount || "0").toLocaleString()} ج.م</span></p>
                  <p>المبلغ المدفوع: <span className="font-medium numbers-ltr text-success">{parseFloat(formData.paidAmount || "0").toLocaleString()} ج.م</span></p>
                  <p>المبلغ المتبقي: <span className={`font-medium numbers-ltr ${parseFloat(formData.remainingAmount || "0") > 0 ? "text-destructive" : "text-success"}`}>{parseFloat(formData.remainingAmount || "0").toLocaleString()} ج.م</span></p>
                </div>
              </div>
            )}
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-4 pt-6 border-t border-border">
            <Button
              type="submit"
              variant="gradient"
              size="lg"
              className="flex-1"
              disabled={isSubmitting}
            >
              <Plus className="w-5 h-5" />
              {isSubmitting ? "جاري الحفظ..." : "حفظ العملية"}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="lg"
              onClick={() => window.history.back()}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default AddTransaction;