import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Plus, Search, Calendar, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";
import { useFilteredTransactions, useTransactionStats, useUniqueCustomers } from "@/hooks/useStats";

const Transactions = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState("");
  const [selectedDate, setSelectedDate] = useState("");

  // استخدام البيانات من التخزين المحلي
  const filteredTransactions = useFilteredTransactions(searchTerm, selectedCustomer, selectedDate);
  const uniqueCustomers = useUniqueCustomers();
  const stats = useTransactionStats(filteredTransactions);

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant="ghost" size="sm">
              <ArrowRight className="w-4 h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">دفتر المعاملات</h1>
            <p className="text-muted-foreground">جميع العمليات التجارية والمبيعات</p>
          </div>
        </div>
        <Link to="/add-transaction">
          <Button variant="gradient" size="lg">
            <Plus className="w-5 h-5" />
            إضافة عملية جديدة
          </Button>
        </Link>
      </div>

      {/* أدوات التصفية */}
      <Card className="p-6 shadow-card">
        <h3 className="text-lg font-semibold mb-4 text-foreground">تصفية المعاملات</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">البحث</label>
            <Input
              placeholder="ابحث باسم التاجر أو المنتج..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">التاجر</label>
            <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
              <SelectTrigger>
                <SelectValue placeholder="جميع التجار" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع التجار</SelectItem>
                {uniqueCustomers.map((customer) => (
                  <SelectItem key={customer} value={customer}>
                    {customer}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">التاريخ</label>
            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-border">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalTransactions}</p>
            <p className="text-sm text-muted-foreground">إجمالي المعاملات</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-success numbers-ltr">{stats.totalAmount.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المبيعات</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalPaid.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المدفوع</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-destructive numbers-ltr">{stats.totalRemaining.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المتبقي</p>
          </div>
        </div>
      </Card>

      {/* جدول المعاملات */}
      <Card className="shadow-card">
        <div className="p-6 border-b border-border">
          <h3 className="text-lg font-semibold text-foreground">
            قائمة المعاملات ({filteredTransactions.length} معاملة)
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full table-rtl">
            <thead className="bg-muted/50">
              <tr>
                <th className="text-right p-4 font-medium text-muted-foreground">#</th>
                <th className="text-right p-4 font-medium text-muted-foreground">التاجر</th>
                <th className="text-right p-4 font-medium text-muted-foreground">المنتج</th>
                <th className="text-right p-4 font-medium text-muted-foreground">الكمية</th>
                <th className="text-right p-4 font-medium text-muted-foreground">السعر/كيلو</th>
                <th className="text-right p-4 font-medium text-muted-foreground">المبلغ الكلي</th>
                <th className="text-right p-4 font-medium text-muted-foreground">المدفوع</th>
                <th className="text-right p-4 font-medium text-muted-foreground">المتبقي</th>
                <th className="text-right p-4 font-medium text-muted-foreground">التاريخ</th>
                <th className="text-right p-4 font-medium text-muted-foreground">الوقت</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.length === 0 ? (
                <tr>
                  <td colSpan={10} className="py-16 text-center text-muted-foreground">
                    <div className="flex flex-col items-center gap-4">
                      <FileText className="w-16 h-16 text-muted-foreground/30" />
                      <div>
                        <p className="text-lg font-medium">لا توجد معاملات</p>
                        <p className="text-sm">
                          {filteredTransactions.length === 0 && !searchTerm && !selectedCustomer && !selectedDate
                            ? "ابدأ بإضافة أول عملية بيع لك"
                            : "لا توجد معاملات مطابقة للبحث"}
                        </p>
                      </div>
                      {filteredTransactions.length === 0 && !searchTerm && !selectedCustomer && !selectedDate && (
                        <Link to="/add-transaction">
                          <Button variant="gradient" size="sm">
                            <Plus className="w-4 h-4" />
                            إضافة عملية بيع
                          </Button>
                        </Link>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                filteredTransactions.map((transaction, index) => (
                  <tr key={transaction.id} className="border-b border-border/50 hover:bg-muted/20 transition-smooth">
                    <td className="p-4 font-medium numbers-ltr">{index + 1}</td>
                    <td className="p-4 font-medium">{transaction.customerName}</td>
                    <td className="p-4">{transaction.product}</td>
                    <td className="p-4 numbers-ltr">{transaction.quantity} كيلو</td>
                    <td className="p-4 numbers-ltr">{transaction.pricePerUnit} ج.م</td>
                    <td className="p-4 numbers-ltr font-medium">
                      {transaction.totalAmount.toLocaleString()} ج.م
                    </td>
                    <td className="p-4 numbers-ltr text-success">
                      {transaction.paidAmount.toLocaleString()} ج.م
                    </td>
                    <td className="p-4 numbers-ltr">
                      <span className={transaction.remainingAmount > 0 ? "text-destructive font-medium" : "text-success"}>
                        {transaction.remainingAmount.toLocaleString()} ج.م
                      </span>
                    </td>
                    <td className="p-4 text-muted-foreground">
                      {new Date(transaction.date).toLocaleDateString('ar-SA')}
                    </td>
                    <td className="p-4 text-muted-foreground numbers-ltr">{transaction.time}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>

      {/* أزرار إضافية */}
      <div className="flex gap-4">
        <Button variant="outline" size="lg">
          <FileText className="w-5 h-5" />
          تصدير PDF
        </Button>
        <Button variant="outline" size="lg">
          <Calendar className="w-5 h-5" />
          تصدير Excel
        </Button>
      </div>
    </div>
  );
};

export default Transactions;