import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, ArrowRight, FileText } from "lucide-react";
import { Link } from "react-router-dom";
import { useFilteredCustomers } from "@/hooks/useStats";
import { useAppStore } from "@/stores/useAppStore";

const Customers = () => {
  const [searchTerm, setSearchTerm] = useState("");

  // استخدام البيانات من التخزين المحلي
  const filteredCustomers = useFilteredCustomers(searchTerm);

  // حساب الإحصائيات
  const stats = {
    totalCustomers: filteredCustomers.length,
    totalDebts: filteredCustomers.reduce((sum, c) => sum + c.remainingDebt, 0),
    totalSales: filteredCustomers.reduce((sum, c) => sum + c.totalPurchases, 0),
    customersWithDebt: filteredCustomers.filter(c => c.remainingDebt > 0).length,
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant="ghost" size="sm">
              <ArrowRight className="w-4 h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">قائمة التجار</h1>
            <p className="text-muted-foreground">إدارة العملاء ومتابعة المعاملات</p>
          </div>
        </div>
      </div>

      {/* أدوات البحث والإحصائيات */}
      <Card className="p-6 shadow-card">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="ابحث عن التاجر..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-primary/5 rounded-lg">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalCustomers}</p>
            <p className="text-sm text-muted-foreground">إجمالي التجار</p>
          </div>
          <div className="text-center p-4 bg-success/5 rounded-lg">
            <p className="text-2xl font-bold text-success numbers-ltr">{stats.totalSales.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المبيعات</p>
          </div>
          <div className="text-center p-4 bg-destructive/5 rounded-lg">
            <p className="text-2xl font-bold text-destructive numbers-ltr">{stats.totalDebts.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي الديون</p>
          </div>
          <div className="text-center p-4 bg-warning/5 rounded-lg">
            <p className="text-2xl font-bold text-warning numbers-ltr">{stats.customersWithDebt}</p>
            <p className="text-sm text-muted-foreground">عليهم ديون</p>
          </div>
        </div>
      </Card>

      {/* قائمة العملاء */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredCustomers.length === 0 ? (
          <Card className="col-span-full p-8 text-center shadow-card">
            <p className="text-muted-foreground">لا توجد نتائج مطابقة للبحث</p>
          </Card>
        ) : (
          filteredCustomers.map((customer) => (
            <Card key={customer.id} className="p-6 shadow-card transition-smooth hover:shadow-elevated">
              <div className="space-y-4">
                {/* معلومات العميل */}
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-1">
                      {customer.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      آخر معاملة: {new Date(customer.lastTransaction).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <FileText className="w-4 h-4" />
                      التفاصيل
                    </Button>
                  </div>
                </div>

                {/* الإحصائيات */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">عدد المعاملات</p>
                    <p className="text-xl font-bold text-primary numbers-ltr">
                      {customer.totalTransactions}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">إجمالي المشتريات</p>
                    <p className="text-xl font-bold text-success numbers-ltr">
                      {customer.totalPurchases.toLocaleString()}
                    </p>
                  </div>
                </div>

                {/* حالة الدفع */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">المبلغ المدفوع:</span>
                    <span className="font-medium text-success numbers-ltr">
                      {customer.totalPaid.toLocaleString()} ج.م
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">المبلغ المتبقي:</span>
                    <span className={`font-bold numbers-ltr ${customer.remainingDebt > 0 ? "text-destructive" : "text-success"}`}>
                      {customer.remainingDebt.toLocaleString()} ج.م
                    </span>
                  </div>
                  
                  {/* شريط التقدم */}
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-success h-2 rounded-full transition-smooth"
                      style={{ 
                        width: `${(customer.totalPaid / customer.totalPurchases) * 100}%` 
                      }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground text-center">
                    {((customer.totalPaid / customer.totalPurchases) * 100).toFixed(1)}% مدفوع
                  </p>
                </div>

                {/* حالة الدين */}
                {customer.remainingDebt > 0 && (
                  <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                    <p className="text-sm text-destructive font-medium">
                      يوجد دين مستحق: {customer.remainingDebt.toLocaleString()} ج.م
                    </p>
                  </div>
                )}
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default Customers;