import React from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Calendar, FileText, Search } from "lucide-react";
import { Link } from "react-router-dom";
import heroImage from "@/assets/accounting-hero.jpg";
import { useDashboardStats } from "@/hooks/useStats";
import { useAppStore } from "@/stores/useAppStore";

const Dashboard = () => {
  // استخدام البيانات الحقيقية من التخزين المحلي
  const stats = useDashboardStats();
  const transactions = useAppStore((state) => state.transactions);

  // أحدث 5 معاملات
  const recentTransactions = transactions
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5);

  return (
    <div className="space-y-8">
      {/* قسم الترحيب والصورة */}
      <Card className="overflow-hidden shadow-elevated">
        <div className="relative h-48 md:h-64 lg:h-80">
          <img
            src={heroImage}
            alt="ترند براند"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/60 flex items-center justify-center">
            <div className="text-center text-primary-foreground px-4">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-3 md:mb-4">
                مرحباً بك في ترند براند
              </h2>
              <p className="text-base md:text-lg lg:text-xl mb-4 md:mb-6 opacity-90">
                إدارة المبيعات والمعاملات التجارية بكل سهولة ووضوح
              </p>
              <p className="text-sm md:text-base opacity-75">
                اليوم هو {new Date().toLocaleDateString('ar-SA')}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">مبيعات اليوم</p>
              <p className="text-2xl font-bold text-success numbers-ltr">
                {stats.todaySales.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg gradient-success flex items-center justify-center">
              <Calendar className="w-6 h-6 text-success-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">إجمالي الديون</p>
              <p className="text-2xl font-bold text-destructive numbers-ltr">
                {stats.totalDebts.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg bg-destructive flex items-center justify-center">
              <FileText className="w-6 h-6 text-destructive-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">مبيعات الشهر</p>
              <p className="text-2xl font-bold text-primary numbers-ltr">
                {stats.thisMonthSales.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg gradient-primary flex items-center justify-center">
              <Calendar className="w-6 h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">عدد التجار</p>
              <p className="text-2xl font-bold text-secondary-accent numbers-ltr">
                {stats.customersCount}
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg bg-secondary-accent flex items-center justify-center">
              <Search className="w-6 h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>
      </div>

      {/* الإجراءات السريعة */}
      <Card className="p-4 md:p-6 shadow-card">
        <h3 className="text-lg md:text-xl font-semibold mb-4 text-foreground">
          الإجراءات السريعة
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
          <Link to="/add-transaction">
            <Button variant="gradient" size="lg" className="w-full h-auto py-3 md:py-4 touch-friendly">
              <div className="flex flex-col items-center gap-2">
                <Plus className="w-5 h-5" />
                <span className="text-xs md:text-sm">إضافة عملية بيع</span>
              </div>
            </Button>
          </Link>
          <Link to="/customers">
            <Button variant="outline" size="lg" className="w-full h-auto py-3 md:py-4 touch-friendly">
              <div className="flex flex-col items-center gap-2">
                <Search className="w-5 h-5" />
                <span className="text-xs md:text-sm">إدارة التجار</span>
              </div>
            </Button>
          </Link>
          <Link to="/transactions">
            <Button variant="outline" size="lg" className="w-full h-auto py-3 md:py-4 touch-friendly">
              <div className="flex flex-col items-center gap-2">
                <FileText className="w-5 h-5" />
                <span className="text-xs md:text-sm">دفتر المعاملات</span>
              </div>
            </Button>
          </Link>
          <Link to="/reports">
            <Button variant="outline" size="lg" className="w-full h-auto py-3 md:py-4 touch-friendly">
              <div className="flex flex-col items-center gap-2">
                <Calendar className="w-5 h-5" />
                <span className="text-xs md:text-sm">التقارير المالية</span>
              </div>
            </Button>
          </Link>
        </div>
      </Card>

      {/* آخر المعاملات */}
      <Card className="p-6 shadow-card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-foreground">
            آخر المعاملات
          </h3>
          <Link to="/transactions">
            <Button variant="ghost" size="sm">
              عرض الكل
            </Button>
          </Link>
        </div>
        
        <div className="overflow-x-auto smooth-scroll">
          <table className="w-full table-rtl">
            <thead>
              <tr className="border-b border-border">
                <th className="text-right py-3 px-2 md:px-4 font-medium text-muted-foreground text-xs md:text-sm">
                  التاجر
                </th>
                <th className="text-right py-3 px-2 md:px-4 font-medium text-muted-foreground text-xs md:text-sm hidden sm:table-cell">
                  المنتج
                </th>
                <th className="text-right py-3 px-2 md:px-4 font-medium text-muted-foreground text-xs md:text-sm hidden md:table-cell">
                  الكمية
                </th>
                <th className="text-right py-3 px-2 md:px-4 font-medium text-muted-foreground text-xs md:text-sm">
                  المبلغ الكلي
                </th>
                <th className="text-right py-3 px-2 md:px-4 font-medium text-muted-foreground text-xs md:text-sm hidden sm:table-cell">
                  المدفوع
                </th>
                <th className="text-right py-3 px-2 md:px-4 font-medium text-muted-foreground text-xs md:text-sm">
                  المتبقي
                </th>
                <th className="text-right py-3 px-2 md:px-4 font-medium text-muted-foreground text-xs md:text-sm hidden lg:table-cell">
                  التاريخ
                </th>
              </tr>
            </thead>
            <tbody>
              {recentTransactions.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-12 text-center text-muted-foreground">
                    <div className="flex flex-col items-center gap-4">
                      <FileText className="w-12 h-12 text-muted-foreground/50" />
                      <div>
                        <p className="text-lg font-medium">لا توجد معاملات حتى الآن</p>
                        <p className="text-sm">ابدأ بإضافة أول عملية بيع لك</p>
                      </div>
                      <Link to="/add-transaction">
                        <Button variant="gradient" size="sm">
                          <Plus className="w-4 h-4" />
                          إضافة عملية بيع
                        </Button>
                      </Link>
                    </div>
                  </td>
                </tr>
              ) : (
                recentTransactions.map((transaction) => (
                  <tr key={transaction.id} className="border-b border-border/50">
                    <td className="py-4 font-medium">{transaction.customerName}</td>
                    <td className="py-4">{transaction.product}</td>
                    <td className="py-4">{transaction.quantity} كيلو</td>
                    <td className="py-4 numbers-ltr">
                      {transaction.totalAmount.toLocaleString()} ج.م
                    </td>
                    <td className="py-4 numbers-ltr text-success">
                      {transaction.paidAmount.toLocaleString()} ج.م
                    </td>
                    <td className="py-4 numbers-ltr">
                      <span className={transaction.remainingAmount > 0 ? "text-destructive" : "text-success"}>
                        {transaction.remainingAmount.toLocaleString()} ج.م
                      </span>
                    </td>
                    <td className="py-4 text-muted-foreground">
                      {new Date(transaction.date).toLocaleDateString('ar-SA')}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;