import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  FileText, 
  Plus, 
  Search 
} from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();

  const menuItems = [
    { path: "/", label: "الرئيسية", icon: Calendar },
    { path: "/transactions", label: "دفتر المعاملات", icon: FileText },
    { path: "/customers", label: "التجار", icon: Search },
    { path: "/products", label: "المنتجات", icon: Plus },
    { path: "/reports", label: "التقارير", icon: FileText },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* الرأس العلوي */}
      <header className="bg-card shadow-card border-b border-border">
        <div className="container mx-auto px-4 py-3 md:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 md:gap-4">
              <div className="w-8 h-8 md:w-10 md:h-10 rounded-lg gradient-primary flex items-center justify-center">
                <FileText className="w-4 h-4 md:w-6 md:h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-lg md:text-2xl font-bold text-foreground">
                  ترند براند
                </h1>
                <p className="text-xs md:text-sm text-muted-foreground hidden sm:block">
                  إدارة العمليات التجارية والمبيعات
                </p>
              </div>
            </div>
            <Link to="/add-transaction">
              <Button variant="gradient" size="sm" className="touch-friendly">
                <Plus className="w-4 h-4" />
                <span className="hidden sm:inline">عملية جديدة</span>
                <span className="sm:hidden">جديد</span>
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* شريط التنقل */}
      <nav className="bg-card border-b border-border overflow-x-auto">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-1 min-w-max md:min-w-0">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;

              return (
                <Link key={item.path} to={item.path}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    className="gap-2 touch-friendly whitespace-nowrap"
                  >
                    <Icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{item.label}</span>
                    <span className="sm:hidden text-xs">
                      {item.label.split(' ')[0]}
                    </span>
                  </Button>
                </Link>
              );
            })}
          </div>
        </div>
      </nav>

      {/* المحتوى الرئيسي */}
      <main className="container mx-auto px-4 py-4 md:py-8">
        {children}
      </main>
    </div>
  );
};

export default Layout;