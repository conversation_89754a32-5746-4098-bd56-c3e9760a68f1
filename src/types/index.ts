// أنواع البيانات للتطبيق

export interface Transaction {
  id: string;
  customerName: string;
  product: string;
  quantity: number;
  pricePerUnit: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  date: string;
  time: string;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  phone?: string;
  address?: string;
  totalTransactions: number;
  totalPurchases: number;
  totalPaid: number;
  remainingDebt: number;
  lastTransaction: string;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  defaultPrice: number;
  unit: string;
  description?: string;
  totalSold: number;
  revenue: number;
  lastSale: string;
  createdAt: string;
  updatedAt: string;
}

export interface DashboardStats {
  todaySales: number;
  totalDebts: number;
  thisMonthSales: number;
  customersCount: number;
  productsCount: number;
  transactionsCount: number;
}

export interface ReportData {
  totalSales: number;
  totalTransactions: number;
  totalPaid: number;
  totalRemaining: number;
  topProducts: Array<{
    name: string;
    quantity: number;
    revenue: number;
  }>;
  topCustomers: Array<{
    name: string;
    transactions: number;
    total: number;
  }>;
}

export interface AppState {
  transactions: Transaction[];
  customers: Customer[];
  products: Product[];
  isLoading: boolean;
  lastUpdated: string;
}
