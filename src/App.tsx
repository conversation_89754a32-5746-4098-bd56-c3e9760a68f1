import React, { useEffect } from "react";
import { Toaster } from "./components/ui/toaster";
import { Toaster as Sonner } from "./components/ui/sonner";
import { TooltipProvider } from "./components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useAppStore } from "./stores/useAppStore";
import Layout from "./components/Layout";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import AddTransaction from "./pages/AddTransaction";
import Transactions from "./pages/Transactions";
import Customers from "./pages/Customers";
import Products from "./pages/Products";
import Reports from "./pages/Reports";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// صفحة رئيسية بسيطة
const HomePage = () => (
  <div className="min-h-screen bg-background text-foreground">
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold text-center mb-8">ترند براند</h1>
      <p className="text-center text-lg text-muted-foreground">
        تطبيق إدارة المبيعات والمعاملات التجارية
      </p>
      <div className="mt-8 text-center">
        <p className="text-green-600">✅ التطبيق يعمل بنجاح!</p>
        <div className="mt-4 space-x-4">
          <a href="/add-transaction" className="text-blue-600 hover:underline">إضافة معاملة</a>
          <a href="/transactions" className="text-blue-600 hover:underline">المعاملات</a>
        </div>
      </div>
    </div>
  </div>
);

const AppContent = () => {
  const loadData = useAppStore((state) => state.loadData);

  useEffect(() => {
    // تحميل البيانات عند بدء التطبيق
    loadData();
  }, [loadData]);

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/add-transaction" element={<Layout><AddTransaction /></Layout>} />
        <Route path="/transactions" element={<Layout><Transactions /></Layout>} />
        <Route path="/customers" element={<Layout><Customers /></Layout>} />
        <Route path="/products" element={<Layout><Products /></Layout>} />
        <Route path="/reports" element={<Layout><Reports /></Layout>} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AppContent />
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
