# دليل استخدام تطبيق ترند براند

## 🚀 البدء السريع

### 1. تشغيل التطبيق
```bash
npm install
npm run dev
```
ثم افتح المتصفح على: `http://localhost:8080`

### 2. أول استخدام
- التطبيق يبدأ فارغاً من البيانات
- ابدأ بإضافة أول عملية بيع من الصفحة الرئيسية
- سيتم إنشاء العملاء والمنتجات تلقائياً

## 📱 الميزات الرئيسية

### 💾 التخزين المحلي
- **جميع البيانات محفوظة محلياً** في متصفحك
- **لا حاجة للإنترنت** بعد التحميل الأول
- **البيانات آمنة** ولا تغادر جهازك أبداً

### ➕ إضافة المعاملات
1. اذهب إلى "إضافة عملية بيع"
2. اختر العميل (أو اكتب اسم جديد)
3. اختر المنتج (أو اكتب منتج جديد)
4. أدخل الكمية والسعر
5. أدخل المبلغ المدفوع
6. اضغط "حفظ العملية"

### 👥 إدارة العملاء
- **تحديث تلقائي**: إحصائيات العملاء تُحدث تلقائياً
- **تتبع الديون**: مراقبة المبالغ المستحقة
- **تاريخ المعاملات**: آخر معاملة لكل عميل

### 📦 إدارة المنتجات
- **إضافة منتجات جديدة** مع الأسعار الافتراضية
- **تتبع المبيعات**: كمية مباعة وإيرادات لكل منتج
- **إحصائيات شاملة**: أداء كل منتج

### 📊 التقارير والإحصائيات
- **تقارير يومية**: مبيعات اليوم الحالي
- **تقارير شهرية**: مبيعات الشهر الحالي
- **أفضل العملاء**: حسب قيمة المشتريات
- **أفضل المنتجات**: حسب الإيرادات

## 🔧 نصائح الاستخدام

### ✅ أفضل الممارسات
1. **أدخل البيانات يومياً** لضمان دقة التقارير
2. **استخدم أسماء ثابتة** للعملاء والمنتجات
3. **راجع التقارير أسبوعياً** لمتابعة الأداء
4. **احتفظ بنسخة احتياطية** من البيانات

### 💡 نصائح مفيدة
- **البحث السريع**: استخدم مربع البحث في كل صفحة
- **التصفية**: رشح المعاملات حسب العميل أو التاريخ
- **الإحصائيات الفورية**: تظهر في أعلى كل صفحة
- **التنقل السريع**: استخدم الأزرار في الشريط العلوي

## 📱 الاستخدام على الهاتف

### تثبيت التطبيق كـ PWA
1. افتح التطبيق في متصفح الهاتف
2. اضغط على قائمة المتصفح (⋮)
3. اختر "إضافة إلى الشاشة الرئيسية"
4. اضغط "إضافة"

### مميزات النسخة المحمولة
- **تصميم متجاوب** للشاشات الصغيرة
- **أزرار محسنة للمس**
- **تمرير سلس** للجداول
- **عمل بدون إنترنت**

## 🔒 الأمان والخصوصية

### حماية البيانات
- **التخزين المحلي**: البيانات لا تغادر جهازك
- **لا توجد خوادم خارجية**: كل شيء يعمل محلياً
- **تشفير المتصفح**: البيانات محمية بتشفير المتصفح

### النسخ الاحتياطي
- **تصدير البيانات**: (قريباً) تصدير كملف JSON
- **استيراد البيانات**: (قريباً) استيراد من ملف
- **مزامنة الأجهزة**: (قريباً) مزامنة بين الأجهزة

## 🆘 حل المشاكل

### مشاكل شائعة
1. **البيانات اختفت**: تحقق من أنك تستخدم نفس المتصفح
2. **التطبيق بطيء**: امسح cache المتصفح
3. **أخطاء في الحفظ**: تحقق من مساحة التخزين المتاحة

### الحصول على المساعدة
- **تحقق من console المتصفح** للأخطاء التقنية
- **أعد تحميل الصفحة** إذا واجهت مشاكل
- **امسح البيانات** من إعدادات المتصفح كحل أخير

## 🔄 التحديثات المستقبلية

### قريباً
- [ ] تصدير/استيراد البيانات
- [ ] طباعة الفواتير
- [ ] إشعارات الديون المستحقة
- [ ] مزامنة السحابة
- [ ] تطبيق أندرويد أصلي

---

**ترند براند** - تطبيق إدارة المبيعات الذكي 🚀
